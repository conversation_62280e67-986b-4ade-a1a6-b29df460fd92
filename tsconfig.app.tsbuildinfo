{"root": ["./src/app.tsx", "./src/index.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/button.tsx", "./src/components/featured-testimonials.tsx", "./src/components/horizontal-gradient.tsx", "./src/components/password.tsx", "./src/components/theme-provider.tsx", "./src/components/color-picker/button.tsx", "./src/components/color-picker/color-control.tsx", "./src/components/color-picker/constants.ts", "./src/components/color-picker/helper.ts", "./src/components/color-picker/helpers.ts", "./src/components/color-picker/index.tsx", "./src/components/color-picker/input.tsx", "./src/components/color-picker/popover.tsx", "./src/components/color-picker/tabs.tsx", "./src/components/color-picker/types.ts", "./src/components/color-picker/color-panel/alpha.tsx", "./src/components/color-picker/color-panel/board.tsx", "./src/components/color-picker/color-panel/index.tsx", "./src/components/color-picker/color-panel/ribbon.tsx", "./src/components/color-picker/color-panel/types.ts", "./src/components/color-picker/gradient/index.tsx", "./src/components/color-picker/gradient-panel/markers.tsx", "./src/components/color-picker/gradient-panel/index.tsx", "./src/components/color-picker/gradient-panel/types.ts", "./src/components/color-picker/solid/index.tsx", "./src/components/color-picker/utils/checkformat.ts", "./src/components/color-picker/utils/color.ts", "./src/components/color-picker/utils/getgradient.ts", "./src/components/color-picker/utils/gethexalpha.ts", "./src/components/color-picker/utils/hextorgba.ts", "./src/components/color-picker/utils/index.ts", "./src/components/color-picker/utils/isvalidhex.ts", "./src/components/color-picker/utils/isvalidrgba.ts", "./src/components/color-picker/utils/parsegradient.ts", "./src/components/color-picker/utils/rgbatoarray.ts", "./src/components/color-picker/utils/rgbatohex.ts", "./src/components/color-picker/utils/usedebounce.ts", "./src/components/color-picker/utils/validgradient.ts", "./src/components/shared/draggable.tsx", "./src/components/shared/icons.tsx", "./src/components/ui/animated-circular-progress.tsx", "./src/components/ui/animated-tooltip.tsx", "./src/components/ui/autosize-input.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/droppable.tsx", "./src/components/ui/file-uploader.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/progressive-file-uploader.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/voice-over/select-options.tsx", "./src/features/editor/editor.tsx", "./src/features/editor/index.ts", "./src/features/editor/menu-list.tsx", "./src/features/editor/navbar.tsx", "./src/features/editor/components/waveformtest.tsx", "./src/features/editor/components/export-button.tsx", "./src/features/editor/components/export-dialog.tsx", "./src/features/editor/components/export-options-dialog.tsx", "./src/features/editor/components/screen-recording.tsx", "./src/features/editor/components/sticky-export-button.tsx", "./src/features/editor/components/timeline-debug.tsx", "./src/features/editor/components/zoom-config-panel.tsx", "./src/features/editor/components/zoom-position-overlay.tsx", "./src/features/editor/constants/constants.ts", "./src/features/editor/constants/events.ts", "./src/features/editor/constants/scale.ts", "./src/features/editor/control-item/animations.tsx", "./src/features/editor/control-item/control-item.tsx", "./src/features/editor/control-item/index.tsx", "./src/features/editor/control-item/presets.tsx", "./src/features/editor/control-item/smart.tsx", "./src/features/editor/data/audio.ts", "./src/features/editor/data/images.ts", "./src/features/editor/data/language.ts", "./src/features/editor/data/transitions.ts", "./src/features/editor/data/uploads.ts", "./src/features/editor/data/video.ts", "./src/features/editor/debug/cpu-debug-panel.tsx", "./src/features/editor/debug/cpu-monitor.ts", "./src/features/editor/debug/debug-panel.tsx", "./src/features/editor/debug/main-thread-monitor.ts", "./src/features/editor/debug/performance-monitor.ts", "./src/features/editor/debug/react-profiler.ts", "./src/features/editor/debug/remotion-profiler.ts", "./src/features/editor/debug/zoom-performance-test.tsx", "./src/features/editor/hooks/is-dragging-over-timeline.tsx", "./src/features/editor/hooks/test-performance.ts", "./src/features/editor/hooks/use-current-frame-optimized.tsx", "./src/features/editor/hooks/use-current-frame.tsx", "./src/features/editor/hooks/use-dynamic-scene-sizing.ts", "./src/features/editor/hooks/use-global-file-drag.tsx", "./src/features/editor/hooks/use-pointer-drag.tsx", "./src/features/editor/hooks/use-screen-recording.ts", "./src/features/editor/hooks/use-timeline-events.ts", "./src/features/editor/hooks/use-update-ansestors.tsx", "./src/features/editor/hooks/use-video-export.ts", "./src/features/editor/hooks/useclickoutside.ts", "./src/features/editor/interfaces/captions.ts", "./src/features/editor/interfaces/editor.ts", "./src/features/editor/interfaces/layout.ts", "./src/features/editor/menu-item/canvas.tsx", "./src/features/editor/menu-item/index.tsx", "./src/features/editor/menu-item/local-audios.tsx", "./src/features/editor/menu-item/local-media.tsx", "./src/features/editor/menu-item/local-videos.tsx", "./src/features/editor/menu-item/menu-item.tsx", "./src/features/editor/player/canvas-container.tsx", "./src/features/editor/player/composition.tsx", "./src/features/editor/player/index.ts", "./src/features/editor/player/media-background.tsx", "./src/features/editor/player/player.tsx", "./src/features/editor/player/sequence-item.tsx", "./src/features/editor/player/styles.ts", "./src/features/editor/player/animated/animated.tsx", "./src/features/editor/player/animated/index.ts", "./src/features/editor/player/animated/presets.ts", "./src/features/editor/player/animated/types.ts", "./src/features/editor/player/animated/useanimation.ts", "./src/features/editor/scene/board.tsx", "./src/features/editor/scene/droppable.tsx", "./src/features/editor/scene/empty.tsx", "./src/features/editor/scene/index.ts", "./src/features/editor/scene/interactions.tsx", "./src/features/editor/scene/scene.tsx", "./src/features/editor/store/use-canvas-store.ts", "./src/features/editor/store/use-layout-store.ts", "./src/features/editor/store/use-local-audios-store.ts", "./src/features/editor/store/use-local-images-store.ts", "./src/features/editor/store/use-local-videos-store.ts", "./src/features/editor/store/use-screen-recording-store.ts", "./src/features/editor/store/use-store.ts", "./src/features/editor/store/use-zoom-store.ts", "./src/features/editor/timeline/header.tsx", "./src/features/editor/timeline/index.ts", "./src/features/editor/timeline/playhead.tsx", "./src/features/editor/timeline/ruler.tsx", "./src/features/editor/timeline/timeline.tsx", "./src/features/editor/timeline/types.ts", "./src/features/editor/timeline/controls/controls.ts", "./src/features/editor/timeline/controls/draw.ts", "./src/features/editor/timeline/controls/index.ts", "./src/features/editor/timeline/items/audio.ts", "./src/features/editor/timeline/items/helper.ts", "./src/features/editor/timeline/items/index.ts", "./src/features/editor/timeline/items/preview-drag-item.ts", "./src/features/editor/timeline/items/timeline.ts", "./src/features/editor/timeline/items/track.ts", "./src/features/editor/timeline/items/video.ts", "./src/features/editor/utils/export-utils.ts", "./src/features/editor/utils/file.ts", "./src/features/editor/utils/filmstrip.ts", "./src/features/editor/utils/format.ts", "./src/features/editor/utils/frames.ts", "./src/features/editor/utils/get-animations.tsx", "./src/features/editor/utils/math.ts", "./src/features/editor/utils/scene.ts", "./src/features/editor/utils/screen-recording-errors.ts", "./src/features/editor/utils/search.ts", "./src/features/editor/utils/target.ts", "./src/features/editor/utils/thumbnail-cache.ts", "./src/features/editor/utils/time.ts", "./src/features/editor/utils/timeline.ts", "./src/features/editor/utils/track-items.ts", "./src/features/editor/utils/webm-metadata.ts", "./src/features/editor/utils/zoom-calculations.ts", "./src/features/editor/utils/zoom-performance.ts", "./src/lib/utils.ts", "./src/remotion/root.tsx", "./src/remotion/videoeditorcomposition.tsx", "./src/services/render-api.ts"], "version": "5.8.3"}